from typing import Annotated
import os
from langchain_core.tools import tool
from langchain.chat_models import init_chat_model
from typing_extensions import TypedDict
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_tavily import TavilySearch
import json
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_core.messages import ToolMessage
from langgraph.types import interrupt
class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]

graph_builder = StateGraph(State)

os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["TAVILY_API_KEY"] = "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"
llm = init_chat_model("deepseek-chat")

@tool
def human_assistance(query: str) -> str:
    """Request assistance from a human."""
    human_response = interrupt({"query": query})
    return human_response["data"]

tool = TavilySearch(max_results=2)
tools = [tool, human_assistance]
# Modification: tell the LLM which tools it can call
# highlight-next-line
llm_with_tools = llm.bind_tools(tools)
tool_node = ToolNode(tools=[tool])
def chatbot(state: State):
    # 检查消息列表是否为空
    if not state["messages"]:
        return {"messages": [{"role": "assistant", "content": "Hello! How can I help you today?"}]}

    message = llm_with_tools.invoke(state["messages"])
    # Because we will be interrupting during tool execution,
    # we disable parallel tool calling to avoid repeating any
    # tool invocations when we resume.
    if hasattr(message, 'tool_calls') and message.tool_calls:
        assert len(message.tool_calls) <= 1
    return {"messages": [message]}

# The first argument is the unique node name
# The second argument is the function or object that will be called whenever
# the node is used.

graph_builder.add_node("chatbot", chatbot)
graph_builder.add_node("tools", tool_node)
graph_builder.add_conditional_edges(
    "chatbot",
    tools_condition,
)
# Any time a tool is called, we return to the chatbot to decide the next step
graph_builder.add_edge("tools", "chatbot")
graph_builder.add_edge(START, "chatbot")
memory = MemorySaver()
graph = graph_builder.compile(checkpointer=memory)



def stream_graph_updates(user_input: str):
    config = {"configurable": {"thread_id": "1"}}
    for event in graph.stream({"messages": [{"role": "user", "content": user_input}]}, config=config, stream_mode="values"):
        if "messages" in event:
            event["messages"][-1].pretty_print()

# 示例用法
if __name__ == "__main__":
    # 测试基本对话
    print("=== 测试基本对话 ===")
    stream_graph_updates("Hello, how are you?")

    print("\n=== 测试搜索功能 ===")
    stream_graph_updates("What's the latest news about artificial intelligence?")
